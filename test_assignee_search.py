#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para probar la búsqueda por Assignee Groups específicamente
"""

import sys
import os
import json
import requests
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

def get_jwt_token():
    """Obtiene el token JWT"""
    api_base_url = 'https://surasoporteti-restapi.onbmc.com/api'
    login_url = f"{api_base_url}/jwt/login"
    username = os.getenv('USERNAME_', 'Integracion.VoiceBot')
    password = os.getenv('PASSWORD', '$ur@2025*')
    
    headers = {'Content-Type': 'application/x-www-form-urlencoded'}
    payload = {
        'username': username,
        'password': password
    }
    
    try:
        response = requests.post(login_url, headers=headers, data=payload)
        response.raise_for_status()
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"Error en la autenticación: {e}")
        return None

def test_assignee_search():
    """Prueba la búsqueda por Assignee Groups"""
    
    token = get_jwt_token()
    if not token:
        print("❌ No se pudo obtener el token")
        return
    
    headers = {
        'Authorization': f'AR-JWT {token}',
        'Content-Type': 'application/json',
        'Cookie': f'AR-JWT={token}'
    }
    
    api_base_url = 'https://surasoporteti-restapi.onbmc.com/api'
    login_id = 'angenogr'
    
    # Probar diferentes formatos de búsqueda
    search_queries = [
        f"'Assignee Groups' LIKE \"%'{login_id}'%\"",
        f"'Assignee Groups' LIKE \"%{login_id}%\"",
        f"'Assignee Groups' LIKE \"%;'{login_id}';%\"",
        f"'Assignee Groups' LIKE \"%angenogr%\"",
    ]
    
    tables = [
        ("HPD:IncidentInterface", "IncidentInterface"),
        ("HPD:Help Desk", "Help Desk")
    ]
    
    for table, table_name in tables:
        print(f"\n🔍 PROBANDO TABLA: {table_name}")
        print("=" * 60)
        
        for i, query in enumerate(search_queries, 1):
            print(f"\n  📋 Prueba {i}: {query}")
            
            url = f"{api_base_url}/arsys/v1/entry/{table}"
            params = {
                "q": query,
                "limit": "10"
            }
            
            try:
                response = requests.get(url, headers=headers, params=params)
                
                print(f"    📊 Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    entries = data.get('entries', [])
                    
                    print(f"    📄 Encontrados: {len(entries)} registros")
                    
                    # Mostrar los primeros registros encontrados
                    for j, entry in enumerate(entries[:3]):
                        values = entry.get('values', {})
                        incident_number = values.get('Incident Number', 'N/A')
                        assignee_groups = values.get('Assignee Groups', 'N/A')
                        status = values.get('Status', 'N/A')
                        
                        print(f"      {j+1}. {incident_number} - {status}")
                        print(f"         Assignee Groups: {assignee_groups}")
                        
                        # Verificar si son los casos que buscamos
                        if incident_number in ['INC000000063179', 'INC000000049083']:
                            print(f"         🎯 ¡ENCONTRADO CASO BUSCADO!")
                    
                    if len(entries) > 3:
                        print(f"      ... y {len(entries) - 3} más")
                        
                else:
                    print(f"    ❌ Error: {response.status_code}")
                    if response.text:
                        print(f"    📄 Respuesta: {response.text[:200]}...")
                        
            except requests.exceptions.RequestException as e:
                print(f"    ❌ Error de conexión: {e}")

def main():
    """Función principal"""
    print("🔍 PROBANDO BÚSQUEDA POR ASSIGNEE GROUPS")
    print("=" * 80)
    
    test_assignee_search()
    
    print("\n" + "=" * 80)
    print("✅ PRUEBA COMPLETADA")

if __name__ == "__main__":
    main()
