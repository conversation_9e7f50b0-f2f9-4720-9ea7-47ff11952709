#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para investigar casos específicos en BMC Remedy
"""

import sys
import os
import json
import requests
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

class CaseInvestigator:
    """Clase para investigar casos específicos en BMC Remedy"""
    
    def __init__(self):
        # Configurar URL base desde variables de entorno (usando producción)
        self.api_base_url = 'https://surasoporteti-restapi.onbmc.com/api'
        
        # Obtener token JWT
        self.token = self._get_jwt_token()
        
        if not self.token:
            raise Exception("No se pudo obtener el token JWT")
        
        # Configurar headers con el token
        self.headers = {
            'Authorization': f'AR-JWT {self.token}',
            'Content-Type': 'application/json',
            'Cookie': f'AR-JWT={self.token}'
        }
    
    def _get_jwt_token(self):
        """Obtiene el token JWT"""
        login_url = f"{self.api_base_url}/jwt/login"
        username = os.getenv('USERNAME_', 'Integracion.VoiceBot')
        password = os.getenv('PASSWORD', '$ur@2025*')
        
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        payload = {
            'username': username,
            'password': password
        }
        
        try:
            response = requests.post(login_url, headers=headers, data=payload)
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            print(f"Error en la autenticación: {e}")
            return None
    
    def search_incident_by_number(self, incident_number):
        """
        Busca un incidente específico por su número usando el formato exacto

        Args:
            incident_number (str): Número del incidente (ej: INC000000063179)

        Returns:
            dict: Información del incidente o None si no se encuentra
        """
        print(f"\n🔍 Buscando incidente: {incident_number}")

        # Buscar en ambas tablas
        tables = [
            ("HPD:Help Desk", "Help Desk"),
            ("HPD:IncidentInterface", "IncidentInterface")
        ]

        results = {}

        for table, table_name in tables:
            print(f"  📋 Buscando en tabla: {table_name}")
            url = f"{self.api_base_url}/arsys/v1/entry/{table}/?q='Incident Number'=\"{incident_number}\""

            print(f"    🌐 URL: {url}")

            try:
                response = requests.get(url, headers=self.headers)

                print(f"    📊 Status Code: {response.status_code}")

                if response.status_code == 200:
                    data = response.json()
                    entries = data.get('entries', [])

                    if entries:
                        print(f"      ✅ Encontrado en {table_name}: {len(entries)} registro(s)")

                        # Mostrar información detallada del primer registro
                        values = entries[0].get('values', {})
                        print(f"      📄 Estado: {values.get('Status', 'N/A')}")
                        print(f"      📄 Prioridad: {values.get('Priority', 'N/A')}")
                        print(f"      📄 Descripción: {values.get('Description', 'N/A')[:100]}...")
                        print(f"      📄 Assignee Groups: {values.get('Assignee Groups', 'N/A')}")
                        print(f"      📄 Direct Contact Corporate ID: {values.get('Direct Contact Corporate ID', 'N/A')}")
                        print(f"      📄 Customer: {values.get('Customer', 'N/A')}")
                        print(f"      📄 Login Id: {values.get('Login Id', 'N/A')}")
                        print(f"      📄 Submitter: {values.get('Submitter', 'N/A')}")
                        print(f"      📄 Create Date: {values.get('Create Date', 'N/A')}")
                        print(f"      📄 Last Modified Date: {values.get('Last Modified Date', 'N/A')}")

                        results[table_name] = entries[0]
                    else:
                        print(f"      ❌ No encontrado en {table_name}")
                else:
                    print(f"      ⚠️  Error {response.status_code} en {table_name}")
                    if response.text:
                        print(f"      📄 Respuesta: {response.text[:200]}...")

            except requests.exceptions.RequestException as e:
                print(f"      ❌ Error de conexión en {table_name}: {e}")

        return results if results else None
    
    def search_by_dwp_number(self, incident_number):
        """
        Busca información adicional usando el número DWP
        """
        print(f"\n🔍 Buscando número DWP para: {incident_number}")
        
        # Buscar en HPD:Help Desk para obtener DWP_SRID
        url = f"{self.api_base_url}/arsys/v1/entry/HPD:Help Desk"
        params = {
            "q": f"'Incident Number'=\"{incident_number}\"",
            "fields": "values(Incident Number,DWP_SRID,Status,Assignee Groups,Direct Contact Corporate ID)"
        }
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                entries = data.get('entries', [])
                
                if entries:
                    values = entries[0]['values']
                    dwp_number = values.get('DWP_SRID')
                    print(f"    📄 Número DWP: {dwp_number}")
                    print(f"    📄 Estado: {values.get('Status')}")
                    print(f"    📄 Assignee Groups: {values.get('Assignee Groups')}")
                    print(f"    📄 Direct Contact Corporate ID: {values.get('Direct Contact Corporate ID')}")
                    return dwp_number
                else:
                    print(f"    ❌ No se encontró información DWP")
            else:
                print(f"    ⚠️  Error {response.status_code} al buscar DWP")
                
        except requests.exceptions.RequestException as e:
            print(f"    ❌ Error de conexión: {e}")
        
        return None
    
    def search_related_to_cedula(self, cedula, incident_number):
        """
        Verifica si el incidente está relacionado con la cédula de alguna manera
        """
        print(f"\n🔍 Verificando relación entre cédula {cedula} e incidente {incident_number}")
        
        # Buscar por diferentes campos que podrían contener la cédula
        search_fields = [
            "'Direct Contact Corporate ID'",
            "'Customer Corporate ID'", 
            "'Submitter Corporate ID'",
            "'Assignee Corporate ID'"
        ]
        
        for field in search_fields:
            print(f"  📋 Buscando por {field}")
            
            url = f"{self.api_base_url}/arsys/v1/entry/HPD:Help Desk"
            params = {
                "q": f"{field}=\"{cedula}\" AND 'Incident Number'=\"{incident_number}\""
            }
            
            try:
                response = requests.get(url, headers=self.headers, params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    entries = data.get('entries', [])
                    
                    if entries:
                        print(f"    ✅ Encontrado por {field}")
                        return True
                    else:
                        print(f"    ❌ No encontrado por {field}")
                else:
                    print(f"    ⚠️  Error {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                print(f"    ❌ Error: {e}")
        
        return False

def main():
    """Función principal"""
    if len(sys.argv) < 2:
        print("Uso: python investigar_casos.py <numero_incidente1> [numero_incidente2] ...")
        print("Ejemplo: python investigar_casos.py INC000000063179 INC000000049083")
        sys.exit(1)
    
    incident_numbers = sys.argv[1:]
    cedula_referencia = "39449326"  # Cédula de Angela Patricia Noreña Grisales
    
    try:
        investigator = CaseInvestigator()
        
        print("=" * 80)
        print("🔍 INVESTIGACIÓN DE CASOS ESPECÍFICOS EN BMC REMEDY")
        print("=" * 80)
        print(f"📋 Casos a investigar: {', '.join(incident_numbers)}")
        print(f"👤 Cédula de referencia: {cedula_referencia}")
        
        for incident_number in incident_numbers:
            print("\n" + "=" * 60)
            print(f"🎯 INVESTIGANDO: {incident_number}")
            print("=" * 60)
            
            # 1. Buscar el incidente directamente
            results = investigator.search_incident_by_number(incident_number)
            
            if results:
                print(f"✅ Incidente {incident_number} EXISTE en el sistema")
                
                # 2. Buscar número DWP
                dwp_number = investigator.search_by_dwp_number(incident_number)
                
                # 3. Verificar relación con la cédula
                is_related = investigator.search_related_to_cedula(cedula_referencia, incident_number)
                
                if is_related:
                    print(f"✅ El incidente {incident_number} ESTÁ relacionado con la cédula {cedula_referencia}")
                else:
                    print(f"❌ El incidente {incident_number} NO está relacionado con la cédula {cedula_referencia}")
                    print("💡 Esto explica por qué no aparece en los reportes de esta cédula")
                
            else:
                print(f"❌ Incidente {incident_number} NO EXISTE en el sistema")
        
        print("\n" + "=" * 80)
        print("✅ INVESTIGACIÓN COMPLETADA")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Error durante la investigación: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
