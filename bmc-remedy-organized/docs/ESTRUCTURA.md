# Estructura del Proyecto BMC Remedy

## Organización de Archivos

Este proyecto ha sido reorganizado para mejorar la mantenibilidad y claridad del código.

### Estructura de Directorios

```
bmc-remedy-organized/
├── main.py                 # Punto de entrada principal
├── src/                    # Código fuente
│   ├── config/             # Configuración
│   ├── core/               # Lógica principal
│   └── utils/              # Utilidades
├── logs/                   # Archivos de log
├── docs/                   # Documentación
└── [archivos de config]    # Docker, requirements, etc.
```

### Módulos

#### src/config/
- `config.py`: Configuración centralizada de la aplicación
- `logging_config.py`: Configuración del sistema de logging

#### src/core/
- `open.py`: Búsqueda y consulta de tickets en BMC Remedy
- `procesar_todo.py`: Procesamiento y coordinación de reportes
- `a.py`: Gestor de tickets y API de BMC

#### src/utils/
- `enviar_reporte.py`: Generación de reportes HTML y envío por correo
- `mail.py`: Funcionalidades de correo electrónico
- `display.py`: Funciones de visualización y formato

## Archivos Omitidos

Los siguientes archivos fueron omitidos de la estructura organizada por ser:
- Archivos de prueba (`test_*.py`)
- Scripts de debug (`investigar_casos.py`)
- Archivos temporales (`debug_reporte.html`, `*.log`)
- Cache de Python (`__pycache__/`)
- Scripts de ejecución redundantes (`run.bat`, `run.sh`)

## Uso

El script principal se ejecuta igual que antes:

```bash
python3.11 main.py 39449326 --destinatario <EMAIL> --todos
```

La nueva estructura mantiene toda la funcionalidad original pero con mejor organización.
