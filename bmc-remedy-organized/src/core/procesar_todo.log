2025-07-10 08:57:08,340 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 08:57:08,340 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 08:57:08,340 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 08:57:08,340 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 08:57:08,340 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 08:57:08,340 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 08:57:09,226 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 08:57:09,229 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 08:57:09,229 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 08:57:37,284 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 08:57:37,284 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 08:57:37,284 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 08:57:37,284 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 08:57:37,284 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 08:57:37,284 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 08:57:38,013 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 08:57:38,017 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 08:57:38,017 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:03:12,025 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:03:12,026 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:03:12,026 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:03:12,026 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:03:12,026 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:03:12,026 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:03:12,757 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:03:12,760 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:03:12,760 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:08:10,786 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:08:10,787 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:08:10,787 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:08:10,787 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:08:10,787 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:08:10,787 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:08:12,801 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:08:12,804 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:08:12,805 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:09:48,910 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:09:48,910 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:09:48,910 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:09:48,910 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:09:48,910 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:09:48,910 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:09:49,564 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:09:49,566 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:09:49,566 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:11:58,433 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:11:58,433 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:11:58,433 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:11:58,433 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:11:58,433 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:11:58,433 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:11:59,053 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:11:59,055 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:11:59,056 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:32:29,043 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:32:29,054 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:32:29,055 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:32:29,055 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:32:29,055 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:32:29,055 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:32:29,055 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:32:30,407 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:32:30,407 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:32:30,407 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:35:29,515 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:35:29,520 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:35:29,520 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:35:29,520 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:35:29,520 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:35:29,521 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:35:29,521 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:35:30,051 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:35:30,052 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:35:30,052 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:37:15,128 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:37:15,135 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:37:15,135 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:37:15,135 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:37:15,136 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:37:15,136 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:37:15,136 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:37:15,974 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:37:15,977 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:37:15,977 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:39:30,403 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:39:30,408 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:39:30,409 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:39:30,409 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:39:30,409 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:39:30,409 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:39:30,409 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:39:31,263 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:39:31,265 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:39:31,266 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:53:26,826 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:53:26,829 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:53:26,830 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:53:26,830 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:53:26,830 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:53:26,830 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:53:26,830 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:53:27,933 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:53:27,936 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:53:27,936 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 12:38:47,184 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:38:47,189 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 12:38:47,189 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 12:38:47,189 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 12:38:47,189 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 12:38:47,189 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 12:38:47,189 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 12:38:47,767 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:38:47,769 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 12:38:47,769 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 12:53:59,474 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:53:59,478 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 12:53:59,479 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 12:53:59,479 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 12:53:59,479 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 12:53:59,479 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 12:53:59,479 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 12:54:00,051 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:54:00,054 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 12:54:00,054 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
