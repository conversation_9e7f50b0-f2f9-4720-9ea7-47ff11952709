2025-07-10 08:57:08,340 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 08:57:08,340 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 08:57:08,340 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 08:57:08,340 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 08:57:08,340 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 08:57:08,340 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 08:57:09,226 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 08:57:09,229 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 08:57:09,229 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 08:57:37,284 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 08:57:37,284 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 08:57:37,284 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 08:57:37,284 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 08:57:37,284 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 08:57:37,284 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 08:57:38,013 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 08:57:38,017 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 08:57:38,017 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
