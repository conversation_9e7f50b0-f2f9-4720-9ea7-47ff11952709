{"user_info": {"cedula": "39449326", "found": true, "first_name": "<PERSON>", "last_name": "Noreña G<PERSON>", "full_name": "<PERSON>", "email": "<EMAIL>", "company": "SURA", "organization": "EPS SURAMERICANA S A"}, "tickets": {"cedula": "39449326", "fecha_consulta": "2025-07-10 09:53:25", "total_tickets": 0, "tickets_procesados": 0, "tickets": []}, "incidents": {"login_id": "39449326", "fecha_consulta": "2025-07-10 09:53:25", "total_incidentes": 8, "incidentes_procesados": 8, "incidentes": [{"incident_id": "INC000000018786", "status": "Closed", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosNo puedo conectarme a la red\nRuta por la que ingresa: NA\nTelefonos de afectado: 3147712899\nUbicación del afectado  (Sede y piso): Viva envigado 2,5\nNombre completo del afectado: Angela <PERSON>\nUsuario con el que ingresó: Angenogr\nDescripción del error: No tenemos navegación. Se fue la energía y quedamos sin red\n¿Cuántas personas hay afectadas?: +30 personas\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:LAN\\nHPD_CI_ReconID: OI-784AF224533211EFB06C1AF001D8BC3E\\nHPD_CI_FormName: BMC_LAN\\nAssociated Alarm: None\\nResolution Category Tier 3: ENERGIA INTERNA\\nClosure Product Category Tier1: Logistica Energia\\nResolution Category Tier 2: FALLA\\nClosure Product Category Tier2: Logistica Energia\\nHPD_CI: RED MICROSOFT\\nClosure Product Category Tier3: Logistica Energia\\nDWP_SRID: 24194\\nDWP_SRInstanceID: AGGJYA2JFNGSXASTOSLRSTOSLROOWS\\nIncident Number: INC000000018786\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Logistica Energia\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Logistica Energia\\nProduct Categorization Tier 3: Logistica Energia\\nProduct Categorization Tier 2: Logistica Energia\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000522;1000000416;1000000451;'angenogr';", "last_modified_date": "2025-03-31T07:02:37.000+0000", "create_date": "2025-03-25T16:05:31.000+0000", "priority": "High", "impact": "2-Significant/Large", "urgency": "2-High", "customer": "<PERSON>", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "<PERSON>rdial saludo,\t\t\n<PERSON><PERSON><PERSON> afectado: Canal de Telecomunicaciones.\t\t\nSede:\tTI Viva Envigado\t\nCiudad:\tEnvigado\t\nResponsable:\tSURAMERICANA\t\nTicket CA:\t6917852\t\nFecha de inicio:\t3/25/2025\t\nFecha de fin:\t3/25/2025\t\nTiempo de Indisponibilidad (min):\t112\t\nTiempo de afectación al usuario (min):\t112\t\nIdentificación del incidente: \tSí\t\nComunicación entre los involucrados: \tSí\t\nDisponibilidad de medios y recursos para la recuperación: \tSolarwinds -\tSin intervención\nDiagnóstico:\t\tPérdida de comunicación con dispositivo\nCausa (Motivo, origen o razón de que suceda el incidente):\t\tEnergia\nSolución:\tSe presenta una alerta en los equipos de la sede SRM TI VIVA ENVIGADO. En validaciones con Coass, nos informan que el centro comercial sufrió una afectación en el suministro de energía eléctrica. Después de varios minutos, la energía retorna, pero los equipos no se encienden. Con el apoyo del equipo de soporte, se verifica que el rack no está energizado y que el técnico eléctrico de la sede había desviado la alimentación a energía comercial. Con el apoyo de Julio Murillo, se restablece la alimentación a energía regulada, se encienden las UPS y comienzan a cargar. Se evidencia la recuperación de los equipos, se mantiene el monitoreo y, tras confirmar la operatividad, se procede con el cierre del caso.\t\nCausa Raíz (Identificada/Sin identificar):\tIdentificada\t\nConfirmar Operatividad:\tSí\t\nEddy Santiago Gomez Castaneda", "resolution_method": null, "dwp_number": "24194", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\n<PERSON><PERSON><PERSON> saludo,\t\t\nServicio afectado: Canal de Telecomunicaciones.\t\t\nSede:\tTI Viva Envigado\t\nCiudad:\tEnvigado\t\nResponsable:\tSURAMERICANA\t\nTicket CA:\t6917852\t\nFecha de inicio:\t3/25/2025\t\nFecha de fin:\t3/25/2025\t\nTiempo de Indisponibilidad (min):\t112\t\nTiempo de afectación al usuario (min):\t112\t\nIdentificación del incidente: \tSí\t\nComunicación entre los involucrados: \tSí\t\nDisponibilidad de medios y recursos para la recuperación: \tSolarwinds -\tSin intervención\nDiagnóstico:\t\tPérdida de comunicación con dispositivo\nCausa (Motivo, origen o razón de que suceda el incidente):\t\tEnergia\nSolución:\tSe presenta una alerta en los equipos de la sede SRM TI VIVA ENVIGADO. En validaciones con Coass, nos informan que el centro comercial sufrió una afectación en el suministro de energía eléctrica. Después de varios minutos, la energía retorna, pero los equipos no se encienden. Con el apoyo del equipo de soporte, se verifica que el rack no está energizado y que el técnico eléctrico de la sede había desviado la alimentación a energía comercial. Con el apoyo de Julio Murillo, se restablece la alimentación a energía regulada, se encienden las UPS y comienzan a cargar. Se evidencia la recuperación de los equipos, se mantiene el monitoreo y, tras confirmar la operatividad, se procede con el cierre del caso.\t\nCausa Raíz (Identificada/Sin identificar):\tIdentificada\t\nConfirmar Operatividad:\tSí\t\nEddy Santiago Gomez Castaneda\n\nCategoría de resolución:\nFALLA ELECTRICA.FALLA.ENERGIA INTERNA", "last_modified_date": "2025-03-26T01:52:55.000+0000"}}, {"incident_id": "INC000000019616", "status": "Closed", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosNavegación desde equipo MAC\nRuta por la que ingresa: Browser\nTelefonos de afectado: 3177062360\nUbicación del afectado  (Sede y piso): Remoto\nNombre completo del afectado: Luis Santiago Grisales\nUsuario con el que ingresó: NA\nDescripción del error: Luis Santiago Grisales se encuentra en reuniones en teams con video y funciona perfecto, pero no le permite navegacion en paginas\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Equipment\\nHPD_CI_ReconID: OI-4D85A864539A11EFB3571AF001D8BC3E\\nHPD_CI_FormName: BMC_EQUIPMENT\\nAssociated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: Gestion Interna\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Sistema Operativo\\nHPD_CI: Portatil\\nClosure Product Category Tier3: Falla\\nDWP_SRID: 25211\\nDWP_SRInstanceID: AGGEFKK2DFA3IASTQPCSSTQPCSLH14\\nIncident Number: INC000000019616\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Gestion Interna\\nCategorization Tier 2: Sistema Operativo\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Gestion Interna\\nProduct Categorization Tier 3: Falla\\nProduct Categorization Tier 2: Sistema Operativo\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000124;'angenogr';", "last_modified_date": "2025-04-02T07:01:08.000+0000", "create_date": "2025-03-26T16:45:30.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "<PERSON>", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): si\nSolución: \n* se validan certificados de mac y se adjuntan al caso , asi mismo se comparten al usuario\n* se sugiere la depuracion de cache y cierre de navegador como la validacion en ambos navegadores\n*usuario expresa que reincia el pc y solventar la novedad\n* usuario aturoriza el cierre del caso\n* se adjuntan soporte de teams y llamadas\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): si", "resolution_method": null, "dwp_number": "25211", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "* se adjunta mas soportes", "last_modified_date": "2025-03-27T12:55:09.000+0000"}}, {"incident_id": "INC000000022437", "status": "Closed", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosBot de teams\nRuta por la que ingresa: Bot de soporte tecnológico virtual en Teams\nTelefonos de afectado: 3147712899\nUbicación del afectado  (Sede y piso): Remoto\nNombre completo del afectado: Angela <PERSON>ua<PERSON> con el que ingresó: <EMAIL>\nDescripción del error: El bot de teams no responde las peticiones\n¿Cuántas personas hay afectadas?: +30 personas\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:CDROMDrive\\nHPD_CI_ReconID: OI-F445A0C0EC2211EFAE6E3ACEF3832554\\nHPD_CI_FormName: BMC_CDROMDRIVE\\nAssociated Alarm: None\\nResolution Category Tier 3: INFORMACION\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: RESTAURAR\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Old_Soporte Tecnológico Bot Teams\\nClosure Product Category Tier3: Indisponibilidad Herramienta\\nDWP_SRID: 28759\\nDWP_SRInstanceID: AGGEFKK2DFA3IASUBXMASUBXMAL0XU\\nIncident Number: INC000000022437\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en Reportes\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000522;1000000416;1000000478;'angenogr';", "last_modified_date": "2025-04-07T07:02:37.000+0000", "create_date": "2025-04-01T18:22:29.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "2-High", "customer": "<PERSON>", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): Vencimiento del token en la integración con Azure\nSolución: Restablecio el ID Secret en azure con fecha a 2 años\nCausa Raíz (Identificada/Sin identificar):Vencimiento de la clave o token\nConfirmar operatividad del usuario Afectado (SI/NO):Si", "resolution_method": null, "dwp_number": "28759", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): Vencimiento del token en la integración con Azure\nSolución: Restablecio el ID Secret en azure con fecha a 2 años\nCausa Raíz (Identificada/Sin identificar):Vencimiento de la clave o token\nConfirmar operatividad del usuario Afectado (SI/NO):Si\n\nCategoría de resolución:\nINFRAESTRUCTURA.RESTAURAR.INFORMACION", "last_modified_date": "2025-04-01T19:42:24.000+0000"}}, {"incident_id": "INC000000040319", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Somos Sura\nRuta por la que ingresa: https://www.sura.co/somos-sura\nTelefonos de afectado: 3147712899\nUbicación del afectado  (Sede y piso): Remoto\nNombre completo del afectado: Angela <PERSON>ua<PERSON> con el que ingresó: angenogr\nDescripción del error: No accede a la página, aparece mensaje de prueba\n¿Cuántas personas hay afectadas?: +30 personas\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-026B5B10892011EF89355E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Gestion Comercial Somos Sura\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Gestion Comercial Somos Sura\\nHPD_CI: SomosSura\\nClosure Product Category Tier3: Gestion Comercial Somos Sura\\nDWP_SRID: 50987\\nDWP_SRInstanceID: AGGDH67PEDJA6ASVWXVRSVWXVRZ1G8\\nIncident Number: INC000000040319\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Somos Sura\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Gestion Comercial Somos Sura\\nProduct Categorization Tier 3: Gestion Comercial Somos Sura\\nProduct Categorization Tier 2: Gestion Comercial Somos Sura\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;'angenogr';", "last_modified_date": "2025-05-08T01:58:39.000+0000", "create_date": "2025-05-07T22:47:08.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "<PERSON>", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buena tarde,", "resolution_method": null, "dwp_number": "50987", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Buena tarde,\n*El caso inicialmente fue reportado como IM\n*Pero posterior a 5 minutos de la notificación, se identificó que se estaban realizando pruebas en SOMOS SURA y se restableció el sistema sin lograr gran afectación del servicio, es por ello, que se desmarcó.\n*Se procede con el cierre.", "last_modified_date": "2025-05-08T01:58:38.000+0000"}}, {"incident_id": "INC000000040427", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosCA Service Desk\nRuta por la que ingresa: https://ca-sd.suramericana.com/CAisd/pdmweb1.exe\nTelefonos de afectado: 3147712899\nUbicación del afectado  (Sede y piso): Remoto\nNombre completo del afectado: <PERSON> Grisales\nUsuario con el que ingresó: angenogr\nDescripción del error: No carga CA SD\n¿Cuántas personas hay afectadas?: +30 personas\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-0374184E892011EF8A265E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Aplicaciones Transversales\\nClosure Product Category Tier2: CA Service Desk\\nHPD_CI: CA Service Desk\\nClosure Product Category Tier3: CA Service Desk\\nDWP_SRID: 51160\\nDWP_SRInstanceID: AGGDH67PEDJA6ASVYBTHSVYBTH3XL3\\nIncident Number: INC000000040427\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: CA - Catalogo de Servicios\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Aplicaciones Transversales\\nProduct Categorization Tier 3: CA Service Desk\\nProduct Categorization Tier 2: CA Service Desk\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000124;'angenogr';", "last_modified_date": "2025-05-08T15:28:57.000+0000", "create_date": "2025-05-08T13:10:24.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "<PERSON>", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "51160", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Buen dia \n\nse establece comunicación via teams \nse valida la afectación \nUsuario indica servicio operativo \nusuario indica darle cierre al caso \n\nFeliz dia", "last_modified_date": "2025-05-08T15:28:57.000+0000"}}, {"incident_id": "INC000000047308", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Equipos de computoPortátil\nRuta por la que ingresa: NA\nTelefonos de afectado: 3147712899\nUbicación del afectado  (Sede y piso): Remoto\nNombre completo del afectado: <PERSON> Grisales\nUsuario con el que ingresó: Angenogr\nDescripción del error: Se quedó bloqueado el equipo desde las 3:52 llevo 25 minutos sin poder trabajar\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: AgendaWeb\\nClosure Product Category Tier2: Administracion Solucion\\nClosure Product Category Tier3: Administracion Solucion\\nDWP_SRID: 59823\\nDWP_SRInstanceID: AGGHE4WHFZM13ASW9BN4SW9BN4C5FW\\nIncident Number: INC000000047308\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: IPS\\nCategorization Tier 2: AgendaWeb\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: AgendaWeb\\nProduct Categorization Tier 3: Administracion Solucion\\nProduct Categorization Tier 2: Administracion Solucion\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;'angenogr';", "last_modified_date": "2025-05-19T21:37:40.000+0000", "create_date": "2025-05-19T21:16:37.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "<PERSON>", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "59823", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000049083", "status": "Closed", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Equipos de computoPortátil\nRuta por la que ingresa: NA\nTelefonos de afectado: 3147712899\nUbicación del afectado  (Sede y piso): Remoto\nNombre completo del afectado: <PERSON> Grisales\nUsuario con el que ingresó: angenogr\nDescripción del error: El equipo presentó pantalla azul, no alcancé a tomar la evidencia, inmediatamente se reinició.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Equipment\\nHPD_CI_ReconID: OI-4D85A864539A11EFB3571AF001D8BC3E\\nHPD_CI_FormName: BMC_EQUIPMENT\\nAssociated Alarm: None\\nResolution Category Tier 3: REPOSICION\\nClosure Product Category Tier1: Hardware\\nResolution Category Tier 2: REPOSICION\\nClosure Product Category Tier2: Portatil\\nHPD_CI: Portatil\\nClosure Product Category Tier3: Portatil\\nDWP_SRID: 62386\\nDWP_SRInstanceID: AGGHE4WHFZM13ASWOEWLSWOEWLD48Z\\nIncident Number: INC000000049083\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Hardware Portatil\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Hardware\\nProduct Categorization Tier 3: Portatil\\nProduct Categorization Tier 2: Portatil\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000164;'angenogr';", "last_modified_date": "2025-07-03T14:40:59.000+0000", "create_date": "2025-05-22T15:14:09.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "<PERSON>", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día\nSe contacta a la usuaria Angela Patricia Noreña Grisales vía Teams\nDiagnóstico: Usuaria indica que el equipo presentó pantallazo azul, después de que se reinició siguió funcionando sin problemas.\nDescarte: Se valida y se evidencia que el equipo se encuentra al día con actualizaciones de drivers, parches de seguridad y sistema operativo, se realizó test de Dell y no presenta alguna falla a nivel de hardware.\nCausa: Fallas de la Board del equipo.\nSolución:\nUsuaria queda operativa con el mismo equipo ya que después de la falla que se le presentó el equipo siguió trabajando correctamente.\nSe observa y el equipo ya no cuenta con garantía.\nEl equipo esta para renovación en Julio por lo cual se genera el catálogo de renovación con número 758897.\nUsuario autoriza realizar el cierre de reporte de falla: Si \nEquipo de recambio: No\nConfirmar operatividad del usuario Afectado: Si \nFeliz día", "resolution_method": null, "dwp_number": "62386", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día\nSe contacta a la usuaria Angela Patricia Noreña Grisales vía Teams\nDiagnóstico: Usuaria indica que el equipo presentó pantallazo azul, después de que se reinició siguió funcionando sin problemas.\nDescarte: Se valida y se evidencia que el equipo se encuentra al día con actualizaciones de drivers, parches de seguridad y sistema operativo, se realizó test de Dell y no presenta alguna falla a nivel de hardware.\nCausa: Fallas de la Board del equipo.\nSolución:\nUsuaria queda operativa con el mismo equipo ya que después de la falla que se le presentó el equipo siguió trabajando correctamente.\nSe observa y el equipo ya no cuenta con garantía.\nEl equipo esta para renovación en Julio por lo cual se genera el catálogo de renovación con número 758897.\nUsuario autoriza realizar el cierre de reporte de falla: Si \nEquipo de recambio: No\nConfirmar operatividad del usuario Afectado: Si \nFeliz día\n\nCategoría de resolución:\nGESTION DE HARDWARE/SOFTWARE.REPOSICION.REPOSICION", "last_modified_date": "2025-05-27T17:37:14.000+0000"}}, {"incident_id": "INC000000063179", "status": "Assigned", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Equipos de computoPortátil\nRuta por la que ingresa: NA\nTelefonos de afectado: 3147712899\nUbicación del afectado  (Sede y piso): Remoto\nNombre completo del afectado: <PERSON> Grisales\nUsuario con el que ingresó: Angenogr\nDescripción del error: prueba reapertura\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 80727\\nDWP_SRInstanceID: AGGHE4WHFZM13ASYAMYMSYAMYMZE6E\\nIncident Number: INC000000063179\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'angenogr';", "last_modified_date": "2025-07-04T15:04:15.000+0000", "create_date": "2025-06-17T19:47:30.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "<PERSON>", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "80727", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-06-18T13:20:14.000+0000"}}]}}