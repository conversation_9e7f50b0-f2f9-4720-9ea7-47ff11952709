import logging
import requests

logger = logging.getLogger()


class Mail:
    """Provides a method to send mails."""

    def __init__(self, to: str = '') -> None:
        """Provides a method to send mails.

        Parameters
        ----------
        to : str, optional
            Recipients, by default ''.
        """
        self.__mail_url = 'https://mosaico.arus.com.co:3000/mailer/correo/notificacion'
        self.__to = to

    def send(self, subject: str, message: str, to: str = '') -> None:
        """Sends a mail.

        Parameters
        ----------
        subject : str
            Subject.
        message : str
            Message.
        to : str, optional
            Recipients, by default ''.
        """
        try:
            recipient = (to if to else self.__to).strip()
            if not recipient:
                logger.warning(
                    'Correo no enviado. Destinatario no establecido.'
                )
                return

            if not subject or not message:
                logger.warning(
                    'Correo no enviado. El asunto y el mensaje son requeridos.'
                )
                return

            data = {'correo': recipient, 'asunto': subject, 'mensaje': message}
            headers = {'Content-Type': 'application/json'}
            response = requests.post(
                self.__mail_url,
                json=data,
                headers=headers,
                verify=False,
                timeout=10,
            )
            response.raise_for_status()

            response_data = response.json()
            assert response_data.get('state') == 'sent', response.text
            logger.info(f'Correo enviado correctamente a {recipient}')
        except Exception as e:
            logger.error(f'No se pudo enviar el correo: {str(e)}')
 
 
 
mail = Mail("<EMAIL>")
if mail.send(
            subject="prueba",
            message="prueba"
        ):
    print("Correo enviado correctamente")
