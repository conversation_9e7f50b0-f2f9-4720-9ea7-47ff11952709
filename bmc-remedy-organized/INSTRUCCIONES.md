# Instrucciones de Uso - BMC Remedy Organizado

## Comando Principal

El script funciona exactamente igual que antes:

```bash
python3.11 main.py 39449326 --destinatario <EMAIL> --todos
```

## Estructura Limpia

✅ **Archivos Organizados:**
- `src/config/` - Configuración y logging
- `src/core/` - Lógica principal (búsqueda, procesamiento)
- `src/utils/` - Utilidades (correo, reportes, visualización)

✅ **Archivos Omitidos:**
- `test_*.py` - Scripts de prueba
- `investigar_casos.py` - Script de debug
- `debug_reporte.html` - Archivo temporal
- `*.log` - Logs antiguos
- `__pycache__/` - <PERSON><PERSON> de <PERSON>
- `run.bat`, `run.sh` - Scripts redundantes

## Configuración

1. Copiar el archivo de configuración:
   ```bash
   cp .env.example .env
   ```

2. Editar `.env` con tus credenciales

## Docker

El proyecto mantiene compatibilidad completa con Docker:

```bash
docker-compose run --rm bmc-remedy-app python main.py 39449326 --destinatario <EMAIL> --todos
```

## Beneficios de la Nueva Estructura

- ✅ Código más organizado y mantenible
- ✅ Separación clara de responsabilidades
- ✅ Eliminación de archivos no utilizados
- ✅ Misma funcionalidad, mejor estructura
- ✅ Compatibilidad completa con Docker
- ✅ Documentación mejorada
